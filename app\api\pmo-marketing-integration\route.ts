import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { collection, query, where, getDocs, doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../../../components/firebase';

/**
 * API endpoint for PMO-Marketing integration
 * Handles the conversion of PMO notifications into marketing collaboration requests
 */

interface PMONotification {
  id: string;
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  status: string;
  notifiedAt: any;
  requiresStrategicPlan: boolean;
  strategicPlanCreated: boolean;
  metadata?: any;
}

interface MarketingCollaborationRequest {
  prompt: string;
  modelProvider: string;
  modelName: string;
  userId: string;
  context: string;
  category: string;
  metadata: {
    source: string;
    pmoId: string;
    notificationId: string;
    autoTriggered: boolean;
    triggerTimestamp: string;
  };
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { notificationId, action } = await req.json();

    if (!notificationId) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      );
    }

    const userId = session.user.email;

    switch (action) {
      case 'trigger-marketing-collaboration':
        return await handleTriggerMarketingCollaboration(userId, notificationId);

      case 'get-marketing-notifications':
        return await handleGetMarketingNotifications(userId);

      case 'update-notification-status':
        return await handleUpdateNotificationStatus(userId, notificationId, req);

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in PMO-Marketing integration:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;
    return await handleGetMarketingNotifications(userId);

  } catch (error) {
    console.error('Error fetching marketing notifications:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to fetch notifications'
      },
      { status: 500 }
    );
  }
}

async function handleTriggerMarketingCollaboration(userId: string, notificationId: string) {
  // Fetch the notification from Firestore
  const notificationsRef = collection(db, 'users', userId, 'teamNotifications');
  const q = query(notificationsRef, where('__name__', '==', notificationId));
  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    return NextResponse.json(
      { error: 'Notification not found' },
      { status: 404 }
    );
  }

  const notificationDoc = querySnapshot.docs[0];
  const notification = { id: notificationDoc.id, ...notificationDoc.data() } as PMONotification;

  // Fetch the full PMO record to get contextCategories and other details
  let pmoRecord = null;
  try {
    const pmoDocRef = doc(db, 'users', userId, 'PMO', notification.pmoId);
    const pmoDocSnap = await getDoc(pmoDocRef);

    if (pmoDocSnap.exists()) {
      pmoRecord = { id: pmoDocSnap.id, ...pmoDocSnap.data() };
      console.log('PMO record fetched successfully:', pmoRecord.id);
      console.log('PMO contextCategories:', pmoRecord.contextCategories);
    } else {
      console.warn('PMO record not found for ID:', notification.pmoId);
    }
  } catch (error) {
    console.error('Error fetching PMO record:', error);
    // Continue without PMO record - use notification data only
  }

  // Verify this is a marketing team notification
  if (notification.teamName?.toLowerCase() !== 'marketing') {
    return NextResponse.json(
      { error: 'This notification is not for the marketing team' },
      { status: 400 }
    );
  }

  // Check if strategic plan already created
  if (notification.strategicPlanCreated) {
    return NextResponse.json(
      { error: 'Marketing collaboration already triggered for this notification' },
      { status: 400 }
    );
  }

  // Construct marketing collaboration request
  const marketingRequest = createMarketingCollaborationRequest(notification, userId, notificationId, pmoRecord);

  // Call the marketing-agent-collaboration API
  const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/marketing-agent-collaboration`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(marketingRequest)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Marketing collaboration API error: ${response.status} - ${errorData.error || response.statusText}`);
  }

  const result = await response.json();

  // Update the notification to mark strategic plan as created
  await updateDoc(doc(db, 'users', userId, 'teamNotifications', notificationId), {
    strategicPlanCreated: true,
    status: 'Strategic Plan Created',
    marketingCollaborationRequestId: result.requestId,
    collaborationTriggeredAt: new Date()
  });

  return NextResponse.json({
    success: true,
    message: 'Marketing collaboration triggered successfully',
    requestId: result.requestId,
    collaborationData: result
  });
}

async function handleGetMarketingNotifications(userId: string) {
  const marketingTasksRef = collection(db, 'users', userId, 'marketingTeamTasks');
  const querySnapshot = await getDocs(marketingTasksRef);

  const notifications: PMONotification[] = [];
  querySnapshot.forEach((doc) => {
    notifications.push({ id: doc.id, ...doc.data() } as PMONotification);
  });

  // Sort by notification date (most recent first)
  notifications.sort((a, b) => {
    const dateA = a.notifiedAt?.toDate?.() || new Date(a.notifiedAt);
    const dateB = b.notifiedAt?.toDate?.() || new Date(b.notifiedAt);
    return dateB.getTime() - dateA.getTime();
  });

  return NextResponse.json({
    success: true,
    notifications,
    count: notifications.length
  });
}

async function handleUpdateNotificationStatus(userId: string, notificationId: string, req: NextRequest) {
  const { status, metadata } = await req.json();

  if (!status) {
    return NextResponse.json(
      { error: 'Status is required' },
      { status: 400 }
    );
  }

  // Update the notification status
  await updateDoc(doc(db, 'users', userId, 'teamNotifications', notificationId), {
    status,
    lastUpdated: new Date(),
    ...(metadata && { metadata: { ...metadata } })
  });

  return NextResponse.json({
    success: true,
    message: 'Notification status updated successfully'
  });
}

function createMarketingCollaborationRequest(
  notification: PMONotification,
  userId: string,
  notificationId: string,
  pmoRecord?: any
): MarketingCollaborationRequest {

  const marketingPrompt = `
# PMO Marketing Requirements Analysis

## Project Overview
**Title:** ${notification.projectTitle}
**Priority:** ${notification.priority}
**PMO ID:** ${notification.pmoId}

## Project Description
${notification.projectDescription}

## PMO Assessment
${notification.pmoAssessment}

## Team Selection Rationale
${notification.teamSelectionRationale}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes:

1. **Strategic Marketing Assessment** - Analyze the marketing implications and opportunities
2. **Target Audience Analysis** - Identify and profile the target market segments
3. **Competitive Landscape** - Research and analyze competitive positioning
4. **Marketing Channel Strategy** - Recommend optimal marketing channels and tactics
5. **Content Strategy** - Develop content themes and messaging framework
6. **Campaign Planning** - Outline campaign structure and timeline
7. **Success Metrics** - Define KPIs and measurement framework
8. **Resource Requirements** - Estimate budget and resource needs

Please ensure your analysis is comprehensive and actionable, providing specific recommendations that the marketing team can implement.
  `.trim();

  const contextInformation = `
PMO Context:
- Project: ${notification.projectTitle}
- Assessment: ${notification.pmoAssessment}
- Priority: ${notification.priority}
- Rationale: ${notification.teamSelectionRationale}
${pmoRecord?.customContext ? `- Custom Context: ${pmoRecord.customContext}` : ''}
${pmoRecord?.contextCategories?.length ? `- Source Document Categories: ${pmoRecord.contextCategories.join(', ')}` : ''}
  `.trim();

  // Use the first contextCategory as the primary category for document search
  const documentCategory = pmoRecord?.contextCategories?.length > 0
    ? pmoRecord.contextCategories[0]
    : notification.category;

  return {
    prompt: marketingPrompt,
    modelProvider: 'openai',
    modelName: 'o3-2025-04-16',
    userId,
    context: contextInformation,
    category: documentCategory, // Use PMO's contextCategories for document search
    metadata: {
      source: 'PMO',
      pmoId: notification.pmoId,
      notificationId,
      autoTriggered: false,
      triggerTimestamp: new Date().toISOString(),
      pmoContextCategories: pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoRecord?.customContext || null
    }
  };
}
