/**
 * API endpoint for committing approved project and tasks creation
 * 
 * This endpoint:
 * 1. Receives approved project data and tasks from the review modal
 * 2. Creates the single project in Firebase
 * 3. Creates only the approved tasks with subtasks
 * 4. Updates PMO record with the created project ID
 * 5. Returns the creation results
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { adminDb } from '../../../components/firebase-admin';
import { addTask } from '../../../app/lib/firebase/planner';
import { Task, TaskPriority, TaskStatus, Subtask } from '../../../admin/planner/types';

interface TaskData {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: 'High' | 'Medium' | 'Low';
  dueDate: string;
  subtasks?: SubtaskData[];
  approved: boolean;
}

interface SubtaskData {
  id: string;
  title: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  dueDate?: string;
  approved: boolean;
}

interface ProjectData {
  projectName: string;
  projectDescription: string;
  startDate: string;
  endDate: string;
  categories: string[];
  priority: 'High' | 'Medium' | 'Low';
  estimatedDuration: string;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { project, approvedTasks, requestId, pmoId, userId }: {
      project: ProjectData;
      approvedTasks: TaskData[];
      requestId: string;
      pmoId?: string;
      userId?: string;
    } = body;

    // Validate required parameters
    if (!project || !approvedTasks || !requestId) {
      return NextResponse.json(
        { error: 'Missing required parameters: project, approvedTasks, requestId' },
        { status: 400 }
      );
    }

    // Use the authenticated user's email or provided userId
    const effectiveUserId = userId || session.user.email;

    console.log(`API: project-creation-commit - Creating project "${project.projectName}" with ${approvedTasks.length} approved tasks`);

    // Step 1: Create the project
    const projectData = {
      name: project.projectName,
      description: project.projectDescription,
      startDate: new Date(project.startDate),
      endDate: new Date(project.endDate),
      owner: effectiveUserId,
      members: [effectiveUserId, '<EMAIL>'], // Include admin user
      categories: project.categories,
      status: 'Active',
      createdAt: new Date(),
      updatedAt: new Date(),
      // PMO-specific fields if applicable
      ...(pmoId && {
        pmoId: pmoId,
        generatedFromAgentOutput: true,
        agentOutputId: requestId
      })
    };

    const projectRef = await adminDb.collection('projects').add(projectData);
    const projectId = projectRef.id;

    console.log(`API: project-creation-commit - Created project with ID: ${projectId}`);

    // Step 2: Create approved tasks
    const createdTasks = [];
    const adminUser = '<EMAIL>'; // ADMIN user as specified

    for (const taskData of approvedTasks) {
      try {
        console.log(`API: project-creation-commit - Creating task: ${taskData.title}`);

        // Calculate dates
        const startDate = new Date();
        const dueDate = new Date(taskData.dueDate);

        // Create approved subtasks
        const approvedSubtasks = taskData.subtasks?.filter(subtask => subtask.approved) || [];
        const subtasks: Subtask[] = approvedSubtasks.map(subtask => ({
          id: `${projectId}_${taskData.id}_${subtask.id}`,
          parentTaskId: '', // Will be set after task creation
          title: subtask.title,
          description: subtask.description,
          status: 'Not Started' as TaskStatus,
          assignedTo: [adminUser],
          priority: subtask.priority as TaskPriority,
          dueDate: subtask.dueDate ? new Date(subtask.dueDate) : undefined,
          createdBy: 'project-creation-commit',
          createdAt: new Date(),
          updatedAt: new Date()
        }));

        // Prepare task data according to Task interface
        const taskToCreate: Omit<Task, 'id'> = {
          projectId,
          title: taskData.title,
          description: taskData.description,
          category: taskData.category,
          status: 'Not Started' as TaskStatus,
          startDate,
          dueDate,
          assignedTo: [adminUser], // Assign to ADMIN user
          priority: taskData.priority as TaskPriority,
          dependencies: [],
          subtasks,
          notes: `Generated from PMO project creation. ${approvedSubtasks.length > 0 ? `Includes ${approvedSubtasks.length} subtasks.` : ''}`,
          createdBy: 'project-creation-commit',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Create the task using the addTask function
        const taskId = await addTask(taskToCreate);

        // Update subtask parentTaskId
        if (subtasks.length > 0) {
          for (const subtask of subtasks) {
            subtask.parentTaskId = taskId;
          }
          // Update the task with correct subtask parentTaskIds
          await adminDb.collection('tasks').doc(taskId).update({
            subtasks: subtasks
          });
        }

        createdTasks.push({
          taskId,
          title: taskData.title,
          projectId,
          description: taskData.description,
          subtasksCreated: subtasks.length
        });

        console.log(`API: project-creation-commit - Successfully created task ${taskData.title} with ID: ${taskId} and ${subtasks.length} subtasks`);

      } catch (error) {
        console.error(`API: project-creation-commit - Error creating task ${taskData.title}:`, error);
        // Continue with other tasks even if one fails
      }
    }

    // Step 3: Update PMO record if applicable
    let pmoUpdated = false;
    if (pmoId) {
      try {
        // Get existing PMO record to append to existing projectIds array
        const pmoDoc = await adminDb.collection('pmo_records').doc(pmoId).get();
        const existingData = pmoDoc.data() || {};
        const existingProjectIds = existingData.projectIds || [];
        const existingProjects = existingData.generatedProjects || [];

        const projectSummary = {
          id: projectId,
          name: project.projectName,
          description: project.projectDescription,
          tasksCreated: createdTasks.length,
          createdAt: new Date().toISOString()
        };

        // Update the PMO record with project information (append to arrays)
        await adminDb.collection('pmo_records').doc(pmoId).update({
          generatedProjects: [...existingProjects, projectSummary],
          projectIds: [...existingProjectIds, projectId], // Support multiple projects
          projectGenerationStatus: 'completed',
          projectGenerationDate: new Date(),
          updatedAt: new Date()
        });

        pmoUpdated = true;
        console.log(`API: project-creation-commit - Updated PMO record ${pmoId} with project ID: ${projectId}`);
      } catch (error) {
        console.error('API: project-creation-commit - Error updating PMO record:', error);
        // Don't fail the entire operation if PMO update fails
      }
    }

    const totalSubtasks = createdTasks.reduce((sum, task) => sum + (task.subtasksCreated || 0), 0);

    console.log(`API: project-creation-commit - Successfully created project "${project.projectName}" with ${createdTasks.length} tasks and ${totalSubtasks} subtasks`);

    return NextResponse.json({
      success: true,
      data: {
        projectId,
        projectName: project.projectName,
        tasksCreated: createdTasks.length,
        subtasksCreated: totalSubtasks,
        pmoUpdated,
        createdTasks: createdTasks.map(task => ({
          taskId: task.taskId,
          title: task.title,
          subtasksCreated: task.subtasksCreated
        }))
      }
    });

  } catch (error: any) {
    console.error('API: project-creation-commit - Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Return API documentation
  return NextResponse.json({
    endpoint: '/api/project-creation-commit',
    description: 'Commits approved project and tasks creation from review modal',
    methods: ['POST'],
    parameters: {
      project: {
        type: 'ProjectData',
        required: true,
        description: 'Project data object with name, description, dates, etc.'
      },
      approvedTasks: {
        type: 'TaskData[]',
        required: true,
        description: 'Array of approved tasks with subtasks'
      },
      requestId: {
        type: 'string',
        required: true,
        description: 'The ID of the Agent_Output document'
      },
      pmoId: {
        type: 'string',
        required: false,
        description: 'Optional PMO record ID to update'
      },
      userId: {
        type: 'string',
        required: false,
        description: 'User ID to assign as project owner (defaults to authenticated user)'
      }
    },
    response: {
      success: 'boolean',
      data: {
        projectId: 'string',
        projectName: 'string',
        tasksCreated: 'number',
        subtasksCreated: 'number',
        pmoUpdated: 'boolean',
        createdTasks: 'Array of created task summaries'
      }
    }
  });
}
