/**
 * PMO Projects Task Agent
 *
 * This agent extracts tasks from Strategic Director Agent outputs and creates them for projects:
 * 1. Retrieves Agent_Output from Firebase collection
 * 2. Uses Groq deepseek LLM to intelligently extract relevant tasks
 * 3. Creates tasks in the tasks collection with proper project linkage
 * 4. Assigns all tasks to ADMIN user (<EMAIL>) with HIGH priority
 * 5. Handles both Marketing and PMO contexts appropriately
 */

import { processWithGroq } from '../tools/groq-ai';
import { adminDb } from '../../components/firebase-admin';
import { addTask } from '../../app/lib/firebase/planner';
import { Task, TaskPriority, TaskStatus } from '../../admin/planner/types';
import { CalendarTool } from '../tools/calendarTool';
import { z } from 'zod';

export interface PMOProjectsTaskAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: PMOTaskStreamUpdate) => void;
}

export interface PMOTaskStreamUpdate {
  stage: 'retrieving-output' | 'extracting-tasks' | 'creating-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface PMOProjectsTaskAgentResult {
  success: boolean;
  tasksCreated: Array<{
    taskId: string;
    title: string;
    projectId: string;
    description: string;
  }>;
  error?: string;
  analysis?: string;
  totalTasksExtracted: number;
}

// Schema for extracted task data
const ExtractedTaskSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: z.string(),
  priority: z.enum(['Low', 'Medium', 'High']).default('High'),
  estimatedDuration: z.string().optional(), // e.g., "3 days", "1 week"
  dependencies: z.array(z.string()).optional(),
  notes: z.string().optional(),
  dueDate: z.string().optional(), // YYYY-MM-DD format
  startDate: z.string().optional() // YYYY-MM-DD format
});

const ExtractedTasksResponseSchema = z.object({
  tasks: z.array(ExtractedTaskSchema),
  analysis: z.string(),
  confidence: z.number().min(0).max(1),
  projectContext: z.string()
});

type ExtractedTask = z.infer<typeof ExtractedTaskSchema>;
type ExtractedTasksResponse = z.infer<typeof ExtractedTasksResponseSchema>;

export class PMOProjectsTaskAgent {
  private options: PMOProjectsTaskAgentOptions;
  private calendarTool: CalendarTool;

  constructor(options: PMOProjectsTaskAgentOptions = {}) {
    this.options = {
      includeExplanation: true,
      streamResponse: false,
      ...options
    };
    this.calendarTool = new CalendarTool();
  }

  /**
   * Extract tasks from agent output without creating them (for preview)
   */
  async extractTasksFromAgentOutput(
    requestId: string,
    projectName: string,
    projectDescription: string
  ): Promise<{
    success: boolean;
    tasksCreated: Array<{
      title: string;
      description: string;
      category: string;
      priority: TaskPriority;
      dueDate: Date;
      subtasks?: Array<{
        title: string;
        description: string;
        priority: TaskPriority;
        dueDate?: Date;
      }>;
    }>;
    error?: string;
    analysis?: string;
  }> {
    try {
      console.log(`PMOProjectsTaskAgent: Extracting tasks for preview from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Extract tasks using Groq deepseek LLM
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis
        };
      }

      // Step 3: Format tasks for preview (without creating them)
      const formattedTasks = await Promise.all(extractedTasks.tasks.map(async task => {
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        return {
          title: task.title,
          description: task.description,
          category: task.category,
          priority: 'High' as TaskPriority, // Always HIGH priority as specified
          dueDate,
          subtasks: [] // TODO: Add subtask extraction logic
        };
      }));

      return {
        success: true,
        tasksCreated: formattedTasks,
        analysis: extractedTasks.analysis
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks for preview:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Create tasks for a project from Strategic Director Agent output
   */
  async createTasksFromAgentOutput(
    requestId: string,
    projectId: string,
    projectName: string,
    projectDescription: string
  ): Promise<PMOProjectsTaskAgentResult> {
    try {
      console.log(`PMOProjectsTaskAgent: Starting task creation for project ${projectId} from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output
      this._streamUpdate('retrieving-output', null, 'Retrieving agent output from Firebase...');
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Extract tasks using Groq deepseek LLM
      this._streamUpdate('extracting-tasks', null, 'Extracting tasks using Groq deepseek LLM...');
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis,
          totalTasksExtracted: 0
        };
      }

      // Step 3: Create tasks in Firebase
      this._streamUpdate('creating-tasks', null, `Creating ${extractedTasks.tasks.length} tasks...`);
      const createdTasks = await this._createTasks(extractedTasks.tasks, projectId, agentOutput);

      this._streamUpdate('complete', null, 'Task creation completed successfully');

      return {
        success: true,
        tasksCreated: createdTasks,
        analysis: extractedTasks.analysis,
        totalTasksExtracted: extractedTasks.tasks.length
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error creating tasks:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error),
        totalTasksExtracted: 0
      };
    }
  }

  /**
   * Retrieve agent output from Firestore
   */
  private async _getAgentOutput(requestId: string): Promise<any> {
    try {
      const doc = await adminDb.collection('Agent_Output').doc(requestId).get();

      if (!doc.exists) {
        throw new Error(`Agent output document not found: ${requestId}`);
      }

      return doc.data();
    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error retrieving agent output:', error);
      throw error;
    }
  }

  /**
   * Extract tasks from agent output using Groq deepseek LLM
   */
  private async _extractTasksWithGroq(
    agentOutput: any,
    projectName: string,
    projectDescription: string
  ): Promise<ExtractedTasksResponse> {
    try {
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';
      const category = agentOutput.category || agentOutput.pmoMetadata?.category || '';

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'yyyy-MM-dd'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      const prompt = `
You are a Task Extraction AI specialized in analyzing Strategic Director Agent outputs to extract specific, actionable tasks for project implementation.

**CURRENT DATE:** ${todayDate}

CONTEXT:
Project Name: ${projectName}
Project Description: ${projectDescription}
Agent Type: ${agentType}
Category: ${category}

AGENT OUTPUT TO ANALYZE:
Output Content: ${outputContent}
Strategic Thinking: ${thinkingContent}

TASK EXTRACTION REQUIREMENTS:
1. Extract SPECIFIC, ACTIONABLE tasks that can be assigned to team members
2. Each task should be concrete and measurable
3. Focus on implementation steps, not high-level strategies
4. Include tasks for planning, execution, monitoring, and evaluation phases
5. Ensure tasks are realistic and achievable
6. All tasks will be assigned to ADMIN user with HIGH priority

RESPONSE FORMAT: Return a valid JSON object with this exact structure:
{
  "tasks": [
    {
      "title": "Specific, actionable task title (max 80 chars)",
      "description": "Detailed description of what needs to be done, including deliverables and success criteria",
      "category": "Marketing|Design|Research|Sales|Admin|Development|Operations",
      "priority": "High",
      "estimatedDuration": "X days/weeks",
      "dependencies": ["Task title that must be completed first"],
      "notes": "Additional context or requirements",
      "dueDate": "YYYY-MM-DD (realistic deadline based on ${todayDate})",
      "startDate": "YYYY-MM-DD (use ${todayDate} or logical start date)"
    }
  ],
  "analysis": "Brief explanation of how tasks were extracted and their alignment with the strategic output",
  "confidence": 0.85,
  "projectContext": "Summary of the project context that influenced task extraction"
}

TASK EXTRACTION GUIDELINES:
- Extract 5-15 specific tasks (avoid too many or too few)
- Task titles should be action-oriented (start with verbs like "Create", "Develop", "Analyze", "Implement")
- Descriptions should include specific deliverables and success criteria
- Categories should match the project's domain and strategic focus
- Dependencies should reference other task titles in the same extraction
- Start dates should be TODAY (${todayDate}) or logical future dates within 1-7 days
- Due dates should be realistic based on task complexity and dependencies (typically 3-30 days from start)
- Use YYYY-MM-DD format for all dates and consider weekends and realistic timelines
- Create a logical workflow sequence with proper task dependencies
- All tasks get HIGH priority as specified in requirements

EXAMPLES OF GOOD TASKS:
- "Create brand messaging framework document"
- "Develop social media content calendar for Q1"
- "Conduct competitor analysis and create comparison matrix"
- "Design landing page wireframes and mockups"
- "Implement email marketing automation workflow"

If no clear tasks can be extracted, return an empty tasks array with appropriate analysis.
`;

      const response = await processWithGroq({
        prompt,
        model: 'deepseek-r1-distill-llama-70b',
        modelOptions: {
          temperature: 0.3,
          max_tokens: 4000,
        },
      });

      // Parse and validate the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Groq response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      return ExtractedTasksResponseSchema.parse(parsedResponse);

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks with Groq:', error);
      throw new Error(`Failed to extract tasks: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create tasks in Firebase using the addTask function
   */
  private async _createTasks(
    extractedTasks: ExtractedTask[],
    projectId: string,
    agentOutput: any
  ): Promise<Array<{ taskId: string; title: string; projectId: string; description: string }>> {
    const createdTasks = [];
    const adminUser = '<EMAIL>'; // ADMIN user as specified

    for (const task of extractedTasks) {
      try {
        console.log(`PMOProjectsTaskAgent: Creating task: ${task.title}`);

        // Calculate dates
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        // Prepare task data according to Task interface
        const taskData: Omit<Task, 'id'> = {
          projectId,
          title: task.title,
          description: task.description,
          category: task.category,
          status: 'Not Started' as TaskStatus,
          startDate,
          dueDate,
          assignedTo: [adminUser], // Assign to ADMIN user
          priority: 'High' as TaskPriority, // Always HIGH priority as specified
          dependencies: task.dependencies || [],
          notes: task.notes || `Generated from Strategic Director Agent output. ${task.estimatedDuration ? `Estimated duration: ${task.estimatedDuration}` : ''}`,
          createdBy: 'pmo-projects-task-agent',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Create the task using the addTask function
        const taskId = await addTask(taskData);

        createdTasks.push({
          taskId,
          title: task.title,
          projectId,
          description: task.description
        });

        console.log(`PMOProjectsTaskAgent: Successfully created task ${task.title} with ID: ${taskId}`);

      } catch (error) {
        console.error(`PMOProjectsTaskAgent: Error creating task ${task.title}:`, error);
        // Continue with other tasks even if one fails
      }
    }

    return createdTasks;
  }

  /**
   * Calculate due date based on start date and estimated duration using Calendar tool
   */
  private async _calculateDueDate(startDate: Date, estimatedDuration?: string): Promise<Date> {
    if (!estimatedDuration) {
      // Default to 7 days if no duration specified
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }

    // Parse duration string (e.g., "3 days", "2 weeks", "1 month")
    const durationMatch = estimatedDuration.match(/(\d+)\s*(day|week|month)s?/i);
    if (durationMatch) {
      const amount = parseInt(durationMatch[1]);
      const unit = durationMatch[2].toLowerCase();

      let daysToAdd = 7; // Default fallback
      switch (unit) {
        case 'day':
          daysToAdd = amount;
          break;
        case 'week':
          daysToAdd = amount * 7;
          break;
        case 'month':
          daysToAdd = amount * 30; // Approximate month as 30 days
          break;
      }

      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    } else {
      // If we can't parse the duration, default to 7 days
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Stream update helper
   */
  private _streamUpdate(stage: PMOTaskStreamUpdate['stage'], data?: any, message?: string) {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }
}

// Export a default instance
export const pmoProjectsTaskAgent = new PMOProjectsTaskAgent({
  includeExplanation: true,
  streamResponse: false
});
