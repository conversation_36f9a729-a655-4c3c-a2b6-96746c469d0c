# PMO Project Creation Fixes

## Problem Summary

The PMO delegation workflow was failing with the following issues:

1. **PMO Record Not Found Error**: `No document to update: projects/indef2024-d11b5/databases/(default)/documents/pmo_records/4ab32657-de13-4c3e-ab7b-712fd166742c`
2. **Multiple Projects Created**: System was creating multiple projects instead of one project with multiple tasks
3. **Missing User Context**: CreateProjectAgent couldn't access PMO records due to incorrect path

## Root Cause Analysis

### 1. PMO Storage Path Mismatch
- **Problem**: PMO records are stored in `users/{userId}/PMO/{pmoId}` but CreateProjectAgent was trying to access them at `pmo_records/{pmoId}`
- **Evidence**: PMO records are created using `addPMORecord()` in `lib/firebase/pmoCollection.ts` which uses the user-specific path
- **Impact**: All PMO record updates failed with "No document to update" error

### 2. Missing User Context in Project Creation
- **Problem**: CreateProjectAgent methods didn't receive the userId needed to access user-specific PMO records
- **Impact**: Even with correct path logic, the system couldn't determine which user's PMO collection to access

### 3. Project Creation Logic
- **Problem**: The system was designed to extract multiple projects from agent outputs, which is correct for general use but not for PMO workflows
- **Solution**: PMO workflows should create ONE project with multiple tasks/subtasks

## Fixes Implemented

### 1. Fixed PMO Record Update Path

**File**: `lib/agents/createProjectAgent.ts`

**Changes**:
- Updated `_updatePMORecord()` method to accept `userId` parameter
- Changed from `adminDb.collection('pmo_records').doc(pmoId)` to `adminDb.doc(\`users/\${userId}/PMO/\${pmoId}\`)`
- Added proper error logging with path information
- Updated method signature: `_updatePMORecord(pmoId: string, userId: string, createdProjects: Array<...>)`

**Before**:
```typescript
const pmoDoc = await adminDb.collection('pmo_records').doc(pmoId).get();
await adminDb.collection('pmo_records').doc(pmoId).update({...});
```

**After**:
```typescript
const pmoDocPath = `users/${userId}/PMO/${pmoId}`;
const pmoDoc = await adminDb.doc(pmoDocPath).get();
await adminDb.doc(pmoDocPath).update({...});
```

### 2. Updated Method Calls to Pass User Context

**Changes**:
- Updated `createProjectsFromAgentOutput()` to pass `userId` to `_updatePMORecord()`
- Updated `_getPMOTitle()` method to accept and use `userId` parameter
- Updated calls to `_getPMOTitle()` to pass `userId`

### 3. Fixed Project Creation Commit Route

**File**: `app/api/project-creation-commit/route.ts`

**Changes**:
- Updated PMO record update logic to use correct user-specific path
- Added proper error handling and logging
- Uses `effectiveUserId` (authenticated user or provided userId)

### 4. Enhanced Error Logging

**Added comprehensive logging**:
- PMO document path being accessed
- User ID and PMO ID in error messages
- Success/failure status for PMO updates
- Clear indication when PMO records are not found

## Project Creation Flow (Fixed)

### 1. PMO "Send to Team" Button Click
- **Location**: `components/PMO/PMORecordList.tsx`
- **Action**: Calls `/api/pmo-notify-team` with PMO record data including `record.id` (PMO ID)

### 2. Team Notification & Auto-Trigger
- **Location**: `app/api/pmo-notify-team/route.ts`
- **Action**: Automatically triggers marketing collaboration for Marketing team
- **Data Flow**: Passes `pmoId` and `userId` to marketing collaboration

### 3. Marketing Agent Collaboration
- **Location**: `app/api/marketing-agent-collaboration/route.ts`
- **Action**: Creates strategic analysis and automatically triggers project creation
- **Key Fix**: Passes both `pmoId` and `userId` to CreateProjectAgent

### 4. Project Creation (Fixed)
- **Location**: `lib/agents/createProjectAgent.ts`
- **Process**:
  1. Retrieves agent output
  2. Gets PMO title using correct user path: `users/{userId}/PMO/{pmoId}`
  3. Creates ONE project (for PMO workflows) with comprehensive description
  4. Creates multiple tasks/subtasks for the project
  5. Updates PMO record using correct path: `users/{userId}/PMO/{pmoId}`

### 5. Preview/Approval Modal (Enhanced)
- **Location**: `components/PMO/ProjectCreationReviewModal.tsx`
- **Action**: Shows project and tasks for user approval
- **Commit**: Uses `/api/project-creation-commit` with fixed PMO update logic

## Testing Verification

To verify the fixes work:

1. **Create a PMO Record**: Use PMO form to create a new record
2. **Send to Marketing Team**: Click "Send to Marketing" button
3. **Monitor Console**: Should see correct PMO path in logs
4. **Verify Project Creation**: Should create ONE project with multiple tasks
5. **Check PMO Record**: Should be updated with project ID in `projectIds` array

## Expected Console Output (Fixed)

```
CreateProjectAgent: Updating PMO record at path: users/<EMAIL>/PMO/4ab32657-de13-4c3e-ab7b-712fd166742c
CreateProjectAgent: Successfully updated PMO record 4ab32657-de13-4c3e-ab7b-712fd166742c with 1 new project IDs
[PROJECT_CREATION] Successfully created 1 projects with 15 tasks
```

### 5. Fixed Project Naming Issue

**Problem**: Projects were not using the PMO title as the project name because the PMO title wasn't being passed through the workflow metadata.

**Files Modified**:
- `app/api/pmo-notify-team/route.ts` - Added `recordTitle` and `projectTitle` to metadata
- `app/api/marketing-agent-collaboration/route.ts` - Included PMO title in agent output metadata

**Changes**:
- PMO title is now passed as `recordTitle` and `projectTitle` in metadata
- Agent output includes both `pmoMetadata` and top-level `metadata` fields
- CreateProjectAgent can now access PMO title via `agentOutput.metadata?.recordTitle`

## Files Modified

1. `lib/agents/createProjectAgent.ts` - Fixed PMO record access paths and user context
2. `app/api/project-creation-commit/route.ts` - Fixed PMO record update in commit route
3. `app/api/pmo-notify-team/route.ts` - Added PMO title to metadata for project naming
4. `app/api/marketing-agent-collaboration/route.ts` - Included PMO title in agent output

## Backward Compatibility

- Non-PMO workflows (without `pmoId`) continue to work as before
- Multiple project extraction still works for general agent outputs
- Single project extraction is used specifically for PMO workflows when `pmoTitle` is available
- Project naming now correctly uses PMO title when available

## Expected Behavior After Fix

1. **PMO Form Submission**: User enters "Develop a marketing campaign for our new SaaS product feature"
2. **Send to Marketing**: PMO record is sent to Marketing team with title preserved
3. **Project Creation**: System creates ONE project named "Develop a marketing campaign for our new SaaS product feature"
4. **Task Generation**: Multiple tasks are created under this single project
5. **PMO Update**: PMO record is updated with the correct project ID

---

**Status**: ✅ Fixed
**Date**: January 2025
**Impact**: Resolves PMO delegation workflow failures and ensures proper project creation with correct naming
